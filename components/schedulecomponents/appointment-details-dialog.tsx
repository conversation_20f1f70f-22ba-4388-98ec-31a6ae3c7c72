"use client"

import {use<PERSON><PERSON>back, useEffect, useState} from "react"
import {<PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Dialog<PERSON>eader, DialogTitle} from "@/components/ui/dialog"
import {Button} from "@/components/ui/button"
import {Textarea} from "@/components/ui/textarea"
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {AlertCircle, AlertTriangle, Info} from "lucide-react"
import {usePatients} from "@/contexts/PatientContext"
import {useAppointments} from "@/contexts/AppointmentContext"
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";

interface AppointmentDetailsDialogProps {
    appointment: ProfessionalAppointment | null
    isOpen: boolean
    onClose: () => void
    doctors: Doctor[]
}

export function AppointmentDetailsDialog({
                                             appointment,
                                             isOpen,
                                             onClose,
                                             doctors
                                         }: AppointmentDetailsDialogProps) {
    const {getPatientById} = usePatients()
    const {appointments} = useAppointments()
    const [patientAppointments, setPatientAppointments] = useState<ProfessionalAppointment[]>([])
    const [showUpPercentage, setShowUpPercentage] = useState(0)

    // State for consultation info dialog
    const [showInstructionsDialog, setShowInstructionsDialog] = useState(false)
    const [instructionsText, setInstructionsText] = useState("")
    const [requiresMedicalOrder, setRequiresMedicalOrder] = useState(false)
    const [copayAmount, setCopayAmount] = useState<number | null>(null)
    const [isConsultationCoverageExcluded, setIsConsultationCoverageExcluded] = useState(false)
    const [selectedConsultationType, setSelectedConsultationType] = useState("")
    const [selectedCoverage, setSelectedCoverage] = useState("")


    const showConsultationInfo = useCallback((appointment: ProfessionalAppointment) => {
        if (appointment.hasConsultationTypeInfo(consultationTypes)) {
            const typesInfo = appointment.getConsultationTypeInfo(consultationTypes);
            setSelectedCoverage(appointment.healthInsuranceInformation);
            // Store all types info for the table display
            setConsultationTypesInfo(typesInfo);
            setShowInstructionsDialog(true);
        }
    }, [consultationTypes]);

    useEffect(() => {
        if (appointment) {
            const patientId = appointment.patient
            const allAppointments = Object.values(appointments).flat()
            const patientAppts = allAppointments.filter((apt) =>
                apt.patient === patientId
            )

            setPatientAppointments(patientAppts)

            // Count only "Ausente" as no-shows
            const noShowAppointments = patientAppts.filter((apt) => apt.status === "Ausente").length
            const totalAppointments = patientAppts.length

            // Calculate attendance percentage: (total - no-shows) / total * 100
            const attendancePercentage = totalAppointments > 0
                ? ((totalAppointments - noShowAppointments) / totalAppointments) * 100
                : 0

            setShowUpPercentage(attendancePercentage)
        }
    }, [appointment, appointments, getPatientById])

    if (!appointment) return null

    const appointmentDate = new Date(`${appointment.date}T${appointment.time}:00`).toLocaleDateString("es-ES", {
        weekday: "long",
        day: "numeric",
        month: "long",
        year: "numeric",
    })

    const patient = getPatientById(appointment.patient)
    const patientName = patient ? patient.name : "Unknown Patient"
    const patientDni = patient ? patient.dni : "N/A"

    const doctor = doctors.find(d => d.id === appointment.doctorId)
    const doctorName = doctor?.name || "N/A"
    const doctorSpecialties = doctor?.specialties.join(", ") || "N/A"

    return (
        <>
            <Dialog open={isOpen} onOpenChange={onClose}>
                <DialogContent className="sm:max-w-[800px] max-h-[70vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Detalles de turno</DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-6">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-medium mb-2">Nombre Completo:</h3>
                                    <p>{patientName}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">DNI:</h3>
                                    <p>{patientDni}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Contacto:</h3>
                                    <p>{appointment.contact}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Cobertura:</h3>
                                    <p>{appointment.coverage}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Estado del turno:</h3>
                                    <p>{appointment.status}</p>
                                </div>
                            </div>
                            <div className="space-y-4">
                                <div>
                                    <h3 className="font-medium mb-2">Fecha y hora del turno:</h3>
                                    <p>{appointmentDate} - {appointment.time}</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Atención:</h3>
                                    <div className="flex items-center">
                                        <p>{appointment.type || "N/A"}</p>
                                        {appointment.type && appointment.coverage && doctor &&
                                            hasConsultationTypeInfo(appointment.type.split(',')[0].trim(), appointment.coverage, doctor) && (
                                                <div
                                                    className="ml-2 cursor-pointer"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        showConsultationInfo(appointment.type.split(',')[0].trim(), appointment.coverage, doctor);
                                                    }}
                                                    aria-label={`Ver información de ${appointment.type.split(',')[0].trim()}`}
                                                    role="button"
                                                    tabIndex={0}
                                                    onKeyDown={(e) => {
                                                        if (e.key === 'Enter' || e.key === ' ') {
                                                            e.stopPropagation();
                                                            showConsultationInfo(appointment.type.split(',')[0].trim(), appointment.coverage, doctor);
                                                        }
                                                    }}
                                                >
                                                    <Info className="h-4 w-4 text-blue-500 hover:text-blue-700"/>
                                                </div>
                                            )}
                                    </div>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Profesional:</h3>
                                    <p>{doctorName} ({doctorSpecialties})</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Atenciones en tu establecimiento:</h3>
                                    <p>{patientAppointments.length} turnos</p>
                                </div>
                                <div>
                                    <h3 className="font-medium mb-2">Porcentaje de Asistencia:</h3>
                                    <p>{showUpPercentage.toFixed(0)}%</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 className="font-medium mb-2">Detalles Adicionales</h3>
                            <Textarea
                                placeholder="Lo escrito por el paciente en 'aclaraciones' desde turnera aparecerá aquí."
                                className="min-h-[100px]"
                                readOnly
                            />
                        </div>
                        <Button onClick={onClose} className="w-full bg-blue-500 hover:bg-blue-600">
                            Cerrar
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

            {/* Instructions Dialog */}
            <AlertDialog open={showInstructionsDialog} onOpenChange={setShowInstructionsDialog}>
                <AlertDialogContent className="max-w-md border border-gray-300 shadow-lg">
                    <AlertDialogHeader className="pb-2 border-b border-gray-200">
                        <AlertDialogTitle className="text-center text-lg font-semibold text-gray-800">Información
                            importante</AlertDialogTitle>
                    </AlertDialogHeader>

                    {/* Content outside of AlertDialogDescription to avoid p > div nesting issue */}
                    <div className="mt-4 space-y-4">
                        {isConsultationCoverageExcluded && (
                            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                                <p className="text-red-700 flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0"/>
                                    <span>
                    La atención <span className="font-semibold">{selectedConsultationType}</span> no es cubierta por el profesional con la cobertura <span
                                        className="font-semibold">{selectedCoverage}</span>.
                  </span>
                                </p>
                            </div>
                        )}

                        {requiresMedicalOrder && (
                            <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                                <p className="text-amber-700 flex items-center gap-2">
                                    <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0"/>
                                    <span>Esta atención requiere orden médica.</span>
                                </p>
                            </div>
                        )}

                        {copayAmount !== null && (
                            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <p className="text-blue-700 flex items-center gap-2">
                                    <Info className="h-5 w-5 text-blue-500 flex-shrink-0"/>
                                    <span>
                    Copago por <span className="font-semibold">{selectedConsultationType}</span> con plan <span
                                        className="font-semibold">{selectedCoverage}</span>: <span
                                        className="font-semibold">${copayAmount}</span>
                  </span>
                                </p>
                            </div>
                        )}

                        {instructionsText && (
                            <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                <div className="text-gray-700">
                                    <p className="font-medium mb-2">Indicaciones para atención <span
                                        className="font-semibold">{selectedConsultationType}</span>:</p>
                                    <p className="whitespace-pre-line">
                                        {instructionsText}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="flex justify-center mt-6">
                        <AlertDialogAction
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-2 rounded-md transition-colors">
                            Entendido
                        </AlertDialogAction>
                    </div>
                </AlertDialogContent>
            </AlertDialog>
        </>
    )
}