"use client"

import {ChevronLeft, ChevronRight, Search} from "lucide-react"
import {Button} from "@/components/ui/button"
import {Input} from "@/components/ui/input"
import {formatMonthYear, generateTimeSlots} from "@/utils/dateUtils"
import {type AppointmentData, NewAppointmentForm} from "@/components/schedulecomponents/new-appointment-form"
import {getWeeksDifference, REFERENCE_DATE} from '@/utils/scheduleUtils'
import {AppointmentState, ProfessionalSchedulesResponse} from "@/types/professional-schedules";
import {PatientResponse} from "@/types/patient/patientResponse";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";

interface SchedulerSidebarProps {
    medicalCenterId: number,
    employeeUserId: number,
    patientSearchTerm: string
    setPatientSearchTerm: (term: string) => void
    isPatientSearchFocused: boolean
    setIsPatientSearchFocused: (focused: boolean) => void
    filteredStoredPatients: PatientResponse[]
    patientsLoading?: boolean
    handlePatientSelect: (patient: PatientResponse) => void
    currentDate: Date
    handlePrevMonth: () => void
    handleNextMonth: () => void
    days: Date[]
    startingDay: number
    isDayAvailable: (day: Date) => boolean
    handleDateSelect: (date: Date) => void
    setIsNewAppointment: (value: boolean) => void
    setShowAgendaOptions: (value: boolean) => void
    isNewAppointment: boolean
    selectedSlot: string | null
    selectedDate: Date
    appointments: { [date: string]: ProfessionalAppointment[] }
    doctorId: number
    professionalSchedules: ProfessionalSchedulesResponse | null
    appointmentDuration: number
    availableSlots: string[]
    // Patient management now handled by PatientContext
    timeSlots: string[]
    handleNewAppointment: (data: AppointmentData) => void
    setSelectedSlot: (slot: string | null) => void
    acceptedCoverageNames: string[]
}

export function SchedulerSidebar({
                                     medicalCenterId,
                                     employeeUserId,
                                     patientSearchTerm,
                                     setPatientSearchTerm,
                                     isPatientSearchFocused,
                                     setIsPatientSearchFocused,
                                     filteredStoredPatients,
                                     patientsLoading,
                                     handlePatientSelect,
                                     currentDate,
                                     handlePrevMonth,
                                     handleNextMonth,
                                     days,
                                     startingDay,
                                     isDayAvailable,
                                     handleDateSelect,
                                     setIsNewAppointment,
                                     setShowAgendaOptions,
                                     isNewAppointment,
                                     selectedSlot,
                                     selectedDate,
                                     appointments,
                                     doctorId,
                                     professionalSchedules,
                                     appointmentDuration,
                                     availableSlots,
                                     // Patient management now handled by PatientContext
                                     timeSlots,
                                     handleNewAppointment,
                                     setSelectedSlot,
                                     acceptedCoverageNames,
                                 }: SchedulerSidebarProps) {

    return (
        <div className="w-[18rem] flex-shrink-0 sticky top-[1.875rem] h-[calc(100vh-7.5rem)] bg-blue-50 z-[40]">
            <div className="h-full sidebar-content">
                {isNewAppointment || selectedSlot ? (
                    <NewAppointmentForm
                        onCancel={() => {
                            setIsNewAppointment(false)
                            setSelectedSlot(null)
                        }}
                        onConfirm={handleNewAppointment}
                        availableSlots={availableSlots}
                        selectedTime={selectedSlot ? selectedSlot.split(':').slice(1).join(':') : null}
                        selectedDate={selectedDate}
                        timeSlots={timeSlots}
                        doctorId={doctorId}
                        medicalCenterId={medicalCenterId}
                        acceptedCoverages={acceptedCoverageNames}
                    />
                ) : (
                    <div className="flex flex-col gap-[1rem]">
                        <div className="relative">
                            <Search
                                className="w-[1rem] h-[1rem] absolute left-[0.75rem] top-1/2 transform -translate-y-1/2 text-gray-400"/>
                            <Input
                                className="pl-[2.25rem] border-black"
                                placeholder="Buscar un paciente en esta agenda"
                                value={patientSearchTerm}
                                onChange={(e) => setPatientSearchTerm(e.target.value)}
                                onFocus={() => setIsPatientSearchFocused(true)}
                                onBlur={() => setTimeout(() => {
                                    setIsPatientSearchFocused(false);
                                    setPatientSearchTerm('');
                                }, 200)}
                            />
                        </div>

                        {patientSearchTerm && isPatientSearchFocused && (
                            <div className="border rounded-lg max-h-[15rem] overflow-y-auto bg-white border-black">
                                {patientsLoading ? (
                                    <div className="p-2 text-center text-gray-500">Buscando...</div>
                                ) : filteredStoredPatients.length > 0 ? (
                                    filteredStoredPatients.map((patient) => (
                                        <Button
                                            key={patient.id}
                                            variant="ghost"
                                            className="w-full justify-start"
                                            onClick={() => handlePatientSelect(patient)}
                                        >
                                            {patient.name} - {patient.identificationNumber}
                                        </Button>
                                    ))
                                ) : (
                                    <div className="p-2 text-center text-gray-500">
                                        {"No se encontraron pacientes"}
                                    </div>
                                )}
                            </div>
                        )}

                        <div className="border rounded-lg p-[1rem] bg-white border-black">
                            <div className="flex items-center justify-between mb-[1rem]">
                                <span className="text-[0.875rem] font-medium">{formatMonthYear(currentDate)}</span>
                                <div className="flex gap-[0.5rem]">
                                    <Button variant="ghost" size="icon" onClick={handlePrevMonth}>
                                        <ChevronLeft className="h-[1rem] w-[1rem]"/>
                                    </Button>
                                    <Button variant="ghost" size="icon" onClick={handleNextMonth}>
                                        <ChevronRight className="h-[1rem] w-[1rem]"/>
                                    </Button>
                                </div>
                            </div>

                            <div className="grid grid-cols-7 gap-[0.375rem] text-[0.875rem] mb-[0.5rem]">
                                {["L", "M", "M", "J", "V", "S", "D"].map((day, i) => (
                                    <div key={i} className="text-center font-medium">
                                        {day}
                                    </div>
                                ))}
                            </div>

                            <div className="grid grid-cols-7 gap-[0.375rem] gap-y-[0.875rem] text-[0.875rem]">
                                {Array.from({length: startingDay}).map((_, i) => (
                                    <div key={`empty-${i}`} className="p-[0.5rem]"/>
                                ))}
                                {days.map((day, i) => {
                                    const dateStr = day.toISOString().split("T")[0]
                                    const appointmentSchedules = professionalSchedules?.getAppointmentSchedulesByDate(day)
                                    const specialSchedules = professionalSchedules?.getSpecialSchedulesByDate(day)
                                    let totalSlots = 0
                                    const appointmentCount = appointments[dateStr]?.filter(apt => apt.state !== AppointmentState.CANCELLED).length || 0
                                    // Check dateExceptions first, then fallback to workingDays
                                    if (specialSchedules) {
                                        specialSchedules.forEach(specialSchedule => {
                                            const start = specialSchedule.startTime
                                            const end = specialSchedule.endTime
                                            totalSlots += generateTimeSlots(start, end, appointmentDuration).length
                                        })
                                    } else if (appointmentSchedules) {
                                        appointmentSchedules.forEach(appointmentSchedule => {
                                            const frequency = appointmentSchedule.weeklyFrequency
                                            const weeksSinceReference = getWeeksDifference(day, REFERENCE_DATE)

                                            if (weeksSinceReference % frequency === 0) {
                                                const start = appointmentSchedule.startTime
                                                const end = appointmentSchedule.endTime
                                                const slots = generateTimeSlots(start, end, appointmentDuration)

                                                totalSlots += slots.length

                                            } else {
                                            }
                                        })
                                    }

                                    const isFull = appointmentCount >= totalSlots && totalSlots > 0
                                    const hasAppointments = appointmentCount > 0 && !isFull
                                    const isSelected =
                                        selectedDate.getDate() === day.getDate() &&
                                        selectedDate.getMonth() === currentDate.getMonth() &&
                                        selectedDate.getFullYear() === currentDate.getFullYear()

                                    // Check if date is in the past
                                    const today = new Date()
                                    today.setHours(0, 0, 0, 0)
                                    const isPastDay = day < today

                                    // Check if date is today
                                    const isToday = day.toDateString() === new Date().toDateString()

                                    return (
                                        <button
                                            key={i}
                                            onClick={() => handleDateSelect(day)}
                                            disabled={!isDayAvailable(day)}
                                            className={`
                        group w-[2rem] h-[2rem] p-0 rounded-full relative
                        ${!isDayAvailable(day) ? "text-gray-300 cursor-not-allowed" : ""}
                        ${isPastDay && !isSelected ? "text-gray-400" : ""}
                        ${isSelected ? "bg-blue-500 text-white" : "hover:bg-blue-50"}
                      `}
                                        >
                                            <div
                                                className={`w-full h-full flex items-center justify-center ${isToday && !isSelected ? "font-semibold" : ""}`}>
                                                {i + 1}
                                            </div>
                                            {(hasAppointments || isFull) && (
                                                <div
                                                    className={`
                            absolute left-1/2 -translate-x-1/2
                            w-[0.375rem] h-[0.375rem] rounded-full
                            transition-all duration-200
                            ${isFull ? "bg-red-500" : "bg-blue-500"}
                            ${isSelected ? "-bottom-[0.625rem]" : "-bottom-[0.125rem] group-hover:-bottom-[0.625rem]"}
                          `}
                                                />
                                            )}
                                        </button>
                                    )
                                })}
                            </div>
                        </div>

                        <Button
                            className="w-full bg-blue-500 hover:bg-blue-600"
                            onClick={() => setIsNewAppointment(true)}
                        >
                            Nuevo Turno
                        </Button>

                        <Button
                            variant="outline"
                            className="w-full border-black"
                            onClick={() => setShowAgendaOptions(true)}
                        >
                            Opciones de Agenda
                        </Button>
                    </div>
                )}
            </div>
        </div>
    )
}