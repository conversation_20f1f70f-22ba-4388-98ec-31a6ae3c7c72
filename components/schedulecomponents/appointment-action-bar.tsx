import {use<PERSON><PERSON>back, useEffect, useRef, useState} from "react"
import {<PERSON>ertCircle, AlertTriangle, Info} from "lucide-react"
import {AppointmentState} from "@/types/professional-schedules"
// Patient type is handled by PatientContext
import type {Doctor} from "@/types/doctor"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {CancelAppointmentDialog} from "@/components/schedulecomponents/cancel-appointment-dialog"
import {AppointmentContextMenu} from "@/components/ui/appointment-context-menu"
import {AppointmentDetailsDialog} from "@/components/schedulecomponents/appointment-details-dialog"
import {AppointmentStatusModal} from "@/components/schedulecomponents/appointment-status-modal"
import {AppointmentModifyModal} from "@/components/schedulecomponents/appointment-modify-modal"
import {getWeeksDifference, REFERENCE_DATE} from '@/utils/scheduleUtils'
import {
    AlertDialog,
    Al<PERSON>DialogAction,
    <PERSON><PERSON><PERSON><PERSON>og<PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eader,
    Al<PERSON><PERSON><PERSON>og<PERSON>it<PERSON>
} from "@/components/ui/alert-dialog"
import {DEFAULT_COVERAGES} from "@/data/coverages"
import {cancelAppointment} from "@/app/api/utils/appointment/SlotApiUtils"
import {useAuth} from "@/contexts/AuthContext"
import {getEmployeeUserId} from "@/utils/userUtils";
import {getSchedules} from "@/app/api/utils/professionalSchedules/ProfessionalSchedulesUtils";
import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {AppointmentConsultationType} from "@/types/consultationTypes/ConsultationType";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";


interface AppointmentActionBarProps {
    onObservationsClick: () => void
    currentStatus: AppointmentState
    onStatusChange: (newStatus: AppointmentState) => void
    onModifyAppointment: (date: Date, time: string, consultationType: string, doctorId: string, duration?: number) => void;
    onCancelAppointment: () => void
    appointment: ProfessionalAppointment
    availableDates: Date[]
    availableTimeSlots: string[]
    contextMenu?: { x: number; y: number } | null
    setContextMenu?: (value: { x: number; y: number } | null) => void
    onAction: (action: string) => void
    view?: "day" | "week" | "month"
    appointments: Record<string, ProfessionalAppointment[]>
    doctors: DoctorsForMedicalCenter[]
    doctorId: number,
    medicalCenterId: number
    onFloatingComponentChange?: (isOpen: boolean) => void
}

export function AppointmentActionBar({
                                         onObservationsClick,
                                         currentStatus,
                                         onStatusChange,
                                         onModifyAppointment,
                                         appointment,
                                         contextMenu,
                                         setContextMenu,
                                         view,
                                         appointments,
                                         doctors,
                                         doctorId,
                                         medicalCenterId,
                                         onFloatingComponentChange,
                                     }: AppointmentActionBarProps) {


    const [floatingComponent, setFloatingComponent] = useState<string | null>(null)
    const [selectedDate, setSelectedDate] = useState<Date>(new Date(appointment.date))
    const [modifyTime, setModifyTime] = useState<string | null>(appointment.startTime)
    const [isDateChanged, setIsDateChanged] = useState(false)
    const [selectedDoctor, setSelectedDoctor] = useState<DoctorsForMedicalCenter | null>(
        doctors.length > 0 ? doctors[0] : null
    )
    const barRef = useRef<HTMLDivElement>(null)
    const [consultationSearch, setConsultationSearch] = useState("")
    const [showConsultationDropdown, setShowConsultationDropdown] = useState(false)
    const [selectedTypes, setSelectedTypes] = useState<AppointmentConsultationType[]>(
        appointment.consultationTypes
    )
    const consultationRef = useRef<HTMLDivElement>(null)
    const {currentUser} = useAuth();


    const [showInstructionsDialog, setShowInstructionsDialog] = useState(false)
    const [instructionsText, setInstructionsText] = useState("")
    const [requiresMedicalOrder, setRequiresMedicalOrder] = useState(false)
    const [copayAmount, setCopayAmount] = useState<number | null>(null)
    const [isConsultationCoverageExcluded, setIsConsultationCoverageExcluded] = useState(false)
    const [selectedConsultationType, setSelectedConsultationType] = useState("")
    const [selectedCoverage, setSelectedCoverage] = useState("")

    const [isCoverageAccepted, setIsCoverageAccepted] = useState<boolean | null>(null)

    const hasConsultationTypeInfo = useCallback((typeName: string, coverage?: string, doctor?: Doctor): boolean => {
        const getSchedulesFromDoctor = (doctorId: number) => {
            const employeeUserId = getEmployeeUserId(currentUser);
            if (employeeUserId) {
                return getSchedules(medicalCenterId, doctorId, employeeUserId, selectedDate).then(response => {
                    setProfessionalSchedules(response);
                    console.log('Professional schedules loaded for month:', getMonthNameFromDate(new Date()), response);
                });
            }
        }

        if (!typeName || !doctor || !doctor.consultationTypes) return false

        const consultationType = doctor.consultationTypes.find(ct => ct.name === typeName)
        if (!consultationType) return false

        // Check if the consultation type has instructions
        const hasInstructions = !!(consultationType.hasInstructions && consultationType.instructions)

        // Check if the consultation type requires a medical order
        const requiresOrder = !!consultationType.requiresMedicalOrder

        // Check for coverage-related info (copays, exclusions)
        let hasCoverageInfo = false

        if (coverage) {
            if (coverage === "Sin Cobertura") {
                // Check if consultation type accepts private pay
                hasCoverageInfo = consultationType.acceptsPrivatePay === false
            } else {
                // For other coverages, check for copays and exclusions
                const foundCoverage = DEFAULT_COVERAGES.find(cov => coverage.startsWith(cov.name))
                if (foundCoverage) {
                    const coverageId = foundCoverage.id

                    // Extract plan if present
                    let planId: string | null = null
                    if (coverage !== foundCoverage.name) {
                        const planPart = coverage.substring(foundCoverage.name.length).trim()
                        if (planPart && foundCoverage.plans.includes(planPart)) {
                            planId = planPart
                        }
                    }

                    // Check for copays
                    const hasCopay = consultationType.copays?.some(
                        c => c.coverageId === coverageId &&
                            (c.planId === null || c.planId === planId)
                    )

                    // Check for exclusions
                    const isExcluded = consultationType.excludedCoverages?.some(
                        exclusion =>
                            exclusion.coverageId === coverageId &&
                            (exclusion.planId === null || exclusion.planId === planId)
                    )

                    hasCoverageInfo = !!hasCopay || !!isExcluded
                }
            }
        }

        return hasInstructions || requiresOrder || hasCoverageInfo
    }, [])

    // Function to show consultation type instructions and requirements
    const showConsultationInfo = useCallback((typeName: string, coverage: string, doctor: Doctor) => {
        const consultationType = doctor.consultationTypes.find(t => t.name === typeName)
        if (consultationType) {
            // Check if there's any info to show
            const hasInstructions = consultationType.hasInstructions && consultationType.instructions
            const requiresOrder = consultationType.requiresMedicalOrder

            // Get current coverage information
            let coverageId = ""
            let planId: string | null = null
            let isExcluded = false
            let copay = null

            if (coverage) {
                if (coverage === "Sin Cobertura") {
                    // Handle Sin Cobertura
                    const sinCobertura = DEFAULT_COVERAGES.find(c => c.name === "Sin Cobertura")
                    if (sinCobertura) {
                        coverageId = sinCobertura.id

                        // Check if this consultation type accepts private pay
                        if (consultationType.acceptsPrivatePay === false) {
                            isExcluded = true
                        }
                    }
                } else {
                    // Extract base coverage name and plan
                    const foundCoverage = DEFAULT_COVERAGES.find(cov => coverage.startsWith(cov.name))
                    if (foundCoverage) {
                        coverageId = foundCoverage.id

                        // Extract plan if present
                        if (coverage !== foundCoverage.name) {
                            const planPart = coverage.substring(foundCoverage.name.length).trim()
                            if (planPart && foundCoverage.plans.includes(planPart)) {
                                planId = planPart
                            }
                        }

                        // Check if this coverage is excluded for this consultation type
                        isExcluded = consultationType.excludedCoverages?.some(
                            exclusion =>
                                exclusion.coverageId === coverageId &&
                                (exclusion.planId === null || exclusion.planId === planId)
                        ) || false

                        // Check if there's a copay for this coverage
                        const foundCopay = consultationType.copays?.find(
                            c => c.coverageId === coverageId &&
                                (c.planId === null || c.planId === planId)
                        )

                        if (foundCopay) {
                            copay = foundCopay.amount
                        }
                    }
                }
            }

            // Update state with coverage and copay information
            setIsConsultationCoverageExcluded(isExcluded)
            setCopayAmount(copay)
            setSelectedConsultationType(typeName)
            setSelectedCoverage(coverage)

            // Update coverage acceptance state
            setIsCoverageAccepted(!isExcluded)

            // Check if there's any info to show (including copay)
            if (!hasInstructions && !requiresOrder && copay === null && !isExcluded) {
                return // Don't show dialog if there's no info
            }

            let dialogText = ""

            // Add instructions if available
            if (hasInstructions) {
                dialogText += consultationType.instructions
            }

            // Set the dialog content
            setInstructionsText(dialogText)
            setRequiresMedicalOrder(requiresOrder)
            setShowInstructionsDialog(true)
        }
    }, [])

    useEffect(() => {
        if (floatingComponent === "modify") {
            const appointmentDate = new Date(appointment.date + "T00:00:00")
            const currentDoctor = doctors.find(d => d.id === doctorId)
            setSelectedDate(appointmentDate)
            setModifyTime(appointment.startTime)
            setIsDateChanged(false)
            setSelectedTypes(appointment.consultationTypes)
            setSelectedDoctor(currentDoctor || null)
            setIsCoverageAccepted(null) // Reset coverage acceptance state
        }
    }, [floatingComponent, appointment, doctors])

    // Check if anything has changed from the original appointment
    const hasChanges = () => {
        if (!selectedDoctor) return false

        // Check if doctor changed
        if (selectedDoctor.id !== doctorId) return true

        // Check if date changed
        const originalDate = new Date(appointment.date + "T00:00:00")
        if (selectedDate.getTime() !== originalDate.getTime()) return true

        // Check if time changed
        if (modifyTime !== appointment.startTime) return true

        // Check if consultation types changed
        const originalTypes = appointment.consultationTypes
        if (selectedTypes.length !== originalTypes.length) return true

        // Check if any type is different
        for (const type of selectedTypes) {
            if (!originalTypes.includes(type)) return true
        }

        return false
    }

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (barRef.current && !barRef.current.contains(e.target as Node) && contextMenu) {
                setContextMenu?.(null)
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [contextMenu, setContextMenu])

    useEffect(() => {
        onFloatingComponentChange?.(!!floatingComponent)
    }, [floatingComponent, onFloatingComponentChange])

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (consultationRef.current && !consultationRef.current.contains(e.target as Node)) {
                setShowConsultationDropdown(false)
                setConsultationSearch("")
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [])

    if (!appointment) return null

    const handleContextAction = (action: string) => {
        setContextMenu?.(null)
        setFloatingComponent(action)
    }

    const isDateAvailable = (date: Date) => {
        if (!selectedDoctor) return false

        const dateStr = date.toISOString().split("T")[0]
        const dayOfWeek = date.getDay().toString()

        // Check if there's an explicit date exception
        if (selectedDoctor.dateExceptions && dateStr in selectedDoctor.dateExceptions) {
            // If it's an extraordinary day (enabled with hours), it's available
            const exception = selectedDoctor.dateExceptions[dateStr]
            if (exception.enabled && exception.hours && exception.hours.length > 0) {
                return true
            }
            // Otherwise, use the enabled status
            return exception.enabled
        }

        const workingDay = selectedDoctor.workingDays[dayOfWeek]

        if (!workingDay?.enabled) return false

        // Use shared getWeeksDifference and REFERENCE_DATE
        const frequency = workingDay.weeksFrequency || 1
        const weeksSinceReference = getWeeksDifference(date, REFERENCE_DATE)
        return weeksSinceReference % frequency === 0
    }


    const getAvailableTimeSlots = () => {
        if (!selectedDoctor || !selectedDate) return [];

        const dateStr = selectedDate.toISOString().split("T")[0];
        const dayOfWeek = selectedDate.getDay().toString();

        // Check for extraordinary day agenda first
        const dateExceptions = selectedDoctor.dateExceptions || {};
        const hasExtraordinaryDay = dateStr in dateExceptions &&
            dateExceptions[dateStr].enabled &&
            dateExceptions[dateStr].hours &&
            dateExceptions[dateStr].hours!.length > 0;

        // Get the hours to use - either from extraordinary day or regular working day
        let hoursToUse: Array<{ start: string; end: string }> = [];

        if (hasExtraordinaryDay) {
            // Use extraordinary day hours
            hoursToUse = dateExceptions[dateStr].hours || [];
        } else {
            // Use regular working day hours if available
            const workingDay = selectedDoctor.workingDays[dayOfWeek];
            if (!workingDay?.enabled || !workingDay.hours || workingDay.hours.length === 0) return [];
            hoursToUse = workingDay.hours;
        }

        // If no hours are available, return empty array
        if (hoursToUse.length === 0) return [];

        const slotDuration = selectedDoctor.appointmentSlotDuration || 15;
        const consultationTypes = selectedDoctor.consultationTypes || [];

        const maxDuration = selectedTypes.length > 0
            ? Math.max(
                ...selectedTypes.map((typeName) => {
                    const consultation = consultationTypes.find((t) => t.name === typeName);
                    const durationValue = consultation?.duration || "default"; // Fallback to "default" if undefined
                    const multiplier = durationValue === "default"
                        ? 1
                        : isNaN(parseInt(durationValue))
                            ? 1
                            : parseInt(durationValue);
                    return slotDuration * multiplier;
                })
            )
            : slotDuration;

        const generateTimeSlots = (start: string, end: string, durationMinutes: number) => {
            const slots: string[] = [];
            const startDate = new Date(`2023-01-01T${start}`);
            const endDate = new Date(`2023-01-01T${end}`);
            endDate.setMinutes(endDate.getMinutes() - durationMinutes);

            const currentTime = new Date(startDate);

            while (currentTime <= endDate) {
                const hours = currentTime.getHours().toString().padStart(2, "0");
                const minutes = currentTime.getMinutes().toString().padStart(2, "0");
                slots.push(`${hours}:${minutes}`);
                currentTime.setMinutes(currentTime.getMinutes() + durationMinutes);
            }

            return slots;
        };

        let allSlots: string[] = [];
        hoursToUse.forEach((hourRange) => {
            const {start, end} = hourRange;
            const rangeSlots = generateTimeSlots(start, end, maxDuration);
            allSlots = [...allSlots, ...rangeSlots];
        });

        return allSlots.sort((a, b) => {
            const timeToMinutes = (time: string) => {
                const [hours, minutes] = time.split(":").map(Number);
                return hours * 60 + minutes;
            };
            return timeToMinutes(a) - timeToMinutes(b);
        });
    };

    const dayHasAppointments = (dateStr: string) => {
        return appointments[dateStr]?.some(
            apt => apt === selectedDoctor?.id && apt.status !== "Cancelado"
        ) || false
    }

    const renderCalendarDays = () => {
        const daysInMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0).getDate()
        const firstDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1)
        const startingDay = (firstDay.getDay() + 6) % 7 // Adjust to start week on Monday

        return [
            ...Array.from({length: startingDay}).map((_, i) => (
                <div key={`empty-${i}`} className="p-2"/>
            )),
            ...Array.from({length: daysInMonth}, (_, i) => {
                const date = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), i + 1)
                const dateStr = date.toISOString().split("T")[0]

                const isAvailable = isDateAvailable(date)
                const hasAppointments = dayHasAppointments(dateStr)
                const isSelected =
                    selectedDate.getDate() === date.getDate() &&
                    selectedDate.getMonth() === date.getMonth() &&
                    selectedDate.getFullYear() === date.getFullYear()

                return (
                    <button
                        key={i}
                        onClick={() => {
                            if (isAvailable) {
                                setSelectedDate(date)
                                setModifyTime(null)
                                setIsDateChanged(true)
                            }
                        }}
                        disabled={!isAvailable}
                        className={`
              group w-8 h-8 p-0 rounded-full relative
              ${!isAvailable ? "text-gray-300 cursor-not-allowed" : ""}
              ${isSelected ? "bg-blue-500 text-white" : "hover:bg-blue-50"}
            `}
                    >
                        <div className="w-full h-full flex items-center justify-center">{i + 1}</div>
                        {hasAppointments && (
                            <div
                                className={`
                  absolute left-1/2 -translate-x-1/2
                  w-[0.375rem] h-[0.375rem] rounded-full
                  transition-all duration-200
                  ${isSelected ? "-bottom-[0.625rem]" : "-bottom-[0.125rem] group-hover:-bottom-[0.625rem]"}
                  bg-blue-500
                `}
                            />
                        )}
                    </button>
                )
            })
        ]
    }

    const handleModifyConfirm = () => {
        if (selectedDoctor && selectedDate && modifyTime && selectedTypes.length > 0) {
            const slotDuration = selectedDoctor.appointmentSlotDuration || 15;
            const consultationTypes = selectedDoctor.consultationTypes || [];
            const maxDuration = Math.max(
                ...selectedTypes.map((typeName) => {
                    const consultation = consultationTypes.find((t) => t.name === typeName);
                    const durationValue = consultation?.duration || "default";
                    const multiplier = consultation?.duration === "default"
                        ? 1
                        : isNaN(parseInt(consultation?.duration || ""))
                            ? 1
                            : parseInt(durationValue);
                    return slotDuration * multiplier;
                })
            );

            onModifyAppointment(
                selectedDate,
                modifyTime,
                selectedTypes.join(", "),
                selectedDoctor.id,
                maxDuration
            );
            setFloatingComponent(null);
        } else {
            console.error("Missing required fields", {
                doctor: !!selectedDoctor,
                date: !!selectedDate,
                time: !!modifyTime,
                types: selectedTypes.length,
            });
        }
    };

    const getConsultationTypes = () => {
        return selectedDoctor?.consultationTypes || []
    }

    return (
        <>
            {view === "day" && (
                <div
                    ref={barRef}
                    className="fixed bottom-12 left-1/2 -translate-x-1/2 bg-blue-50 rounded-lg border shadow-lg z-50 py-2 px-4 w-[calc(100%-24rem)] max-w-3xl"
                >
                    <div className="flex items-center justify-center gap-4">
                        <Button variant="outline" onClick={() => setFloatingComponent("status")}>
                            Estado de turno
                        </Button>
                        <Button variant="outline" onClick={onObservationsClick}>
                            Detalles de turno
                        </Button>
                        <Button variant="outline" onClick={() => setFloatingComponent("modify")}>
                            Modificar turno
                        </Button>
                        <Button
                            variant="outline"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => setFloatingComponent("cancel")}
                        >
                            Cancelar turno
                        </Button>
                    </div>
                </div>
            )}

            {contextMenu && view === "week" && (
                <AppointmentContextMenu
                    x={contextMenu.x}
                    y={contextMenu.y}
                    onAction={handleContextAction}
                    onClose={() => setContextMenu?.(null)}
                />
            )}

            {floatingComponent === "status" && (
                <AppointmentStatusModal
                    isOpen={true}
                    onClose={() => setFloatingComponent(null)}
                    currentStatus={currentStatus}
                    onStatusChange={onStatusChange}
                />
            )}

            {floatingComponent === "observations" && (
                <div className="fixed inset-0 bg-black/50 z-50">
                    <AppointmentDetailsDialog
                        appointment={appointment}
                        isOpen={true}
                        onClose={() => setFloatingComponent(null)}
                        // Patient management now handled by PatientContext
                        doctors={doctors}
                    />
                </div>
            )}

            {floatingComponent === "modify" && (
                <AppointmentModifyModal
                    isOpen={true}
                    onClose={() => setFloatingComponent(null)}
                    appointment={appointment}
                    doctors={doctors}
                    doctorId={doctorId}
                    appointments={appointments}
                    onModifyAppointment={onModifyAppointment}
                />
            )}

            {floatingComponent === "cancel" && (
                <CancelAppointmentDialog
                    isOpen={true}
                    onClose={() => setFloatingComponent(null)}
                    patient={{name: appointment.patientName, id: appointment.id.toString()}}
                    onConfirm={(option, reason) => {
                        // Use the new cancelAppointment function instead of removeAppointment
                        cancelAppointment(appointment.id, reason)

                        // Log the cancellation details
                        console.log(`Cancellation option: ${option}${reason ? `, reason: ${reason}` : ''}`)

                        // Don't call onCancelAppointment() to avoid double cancellation
                        // The cancelAppointment function already handles everything including notifications
                        // The UI will update automatically when the appointment state changes
                        setFloatingComponent(null)
                    }}
                />
            )}

            {/* Instructions Dialog */}
            <AlertDialog open={showInstructionsDialog} onOpenChange={setShowInstructionsDialog}>
                <AlertDialogContent className="max-w-md border border-gray-300 shadow-lg">
                    <AlertDialogHeader className="pb-2 border-b border-gray-200">
                        <AlertDialogTitle className="text-center text-lg font-semibold text-gray-800">Información
                            importante</AlertDialogTitle>
                    </AlertDialogHeader>

                    {/* Content outside of AlertDialogDescription to avoid p > div nesting issue */}
                    <div className="mt-4 space-y-4">
                        {isConsultationCoverageExcluded && (
                            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                                <p className="text-red-700 flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0"/>
                                    <span>
                    La atención <span className="font-semibold">{selectedConsultationType}</span> no es cubierta por el profesional con la cobertura <span
                                        className="font-semibold">{selectedCoverage}</span>.
                  </span>
                                </p>
                            </div>
                        )}

                        {requiresMedicalOrder && (
                            <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                                <p className="text-amber-700 flex items-center gap-2">
                                    <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0"/>
                                    <span>Esta atención requiere orden médica.</span>
                                </p>
                            </div>
                        )}

                        {copayAmount !== null && (
                            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <p className="text-blue-700 flex items-center gap-2">
                                    <Info className="h-5 w-5 text-blue-500 flex-shrink-0"/>
                                    <span>
                    Copago por <span className="font-semibold">{selectedConsultationType}</span> con plan <span
                                        className="font-semibold">{selectedCoverage}</span>: <span
                                        className="font-semibold">${copayAmount}</span>
                  </span>
                                </p>
                            </div>
                        )}

                        {instructionsText && (
                            <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                <div className="text-gray-700">
                                    <p className="font-medium mb-2">Indicaciones para atención <span
                                        className="font-semibold">{selectedConsultationType}</span>:</p>
                                    <p className="whitespace-pre-line">
                                        {instructionsText}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="flex justify-center mt-6">
                        <AlertDialogAction
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-2 rounded-md transition-colors">
                            Entendido
                        </AlertDialogAction>
                    </div>
                </AlertDialogContent>
            </AlertDialog>
        </>
    )
}
