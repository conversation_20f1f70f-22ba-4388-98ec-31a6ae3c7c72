import {use<PERSON><PERSON>back, useEffect, useRef, useState} from "react"
import {AlertCircle, AlertTriangle, ChevronLeft, ChevronRight, Info, Search} from "lucide-react"
import {Input} from "@/components/ui/input"
import type {Doctor} from "@/types/doctor"
import {<PERSON><PERSON>} from "@/components/ui/button"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Label} from "@/components/ui/label"
import {getWeeksDifference, REFERENCE_DATE} from '@/utils/scheduleUtils'
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogHeader,
    AlertDialogTitle
} from "@/components/ui/alert-dialog"
import {cn} from "@/lib/utils"
import {DEFAULT_COVERAGES} from "@/data/coverages"

import {DoctorsForMedicalCenter} from "@/types/doctors/doctorsForMedicalCenter";
import {AppointmentConsultationType} from "@/types/consultationTypes/ConsultationType";
import {ProfessionalAppointment} from "@/types/appointment/professional-appointment";

interface AppointmentModifyModalProps {
    isOpen: boolean
    onClose: () => void
    appointment: ProfessionalAppointment
    doctors: DoctorsForMedicalCenter[]
    doctorId: number
    appointments: Record<string, ProfessionalAppointment[]>
    onModifyAppointment: (date: Date, time: string, consultationType: string, doctorId: string, duration?: number) => void
}

export function AppointmentModifyModal({
                                           isOpen,
                                           onClose,
                                           appointment,
                                           doctors,
                                           doctorId,
                                           appointments,
                                           onModifyAppointment
                                       }: AppointmentModifyModalProps) {
    const [selectedDate, setSelectedDate] = useState<Date>(new Date(appointment.date))
    const [modifyTime, setModifyTime] = useState<string | null>(appointment.startTime)
    const [isDateChanged, setIsDateChanged] = useState(false)
    const [selectedDoctor, setSelectedDoctor] = useState<DoctorsForMedicalCenter | null>(
        doctors.length > 0 ? doctors[0] : null
    )
    const [consultationSearch, setConsultationSearch] = useState("")
    const [showConsultationDropdown, setShowConsultationDropdown] = useState(false)
    const [selectedTypes, setSelectedTypes] = useState<AppointmentConsultationType[]>(
        appointment.consultationTypes
    )
    const consultationRef = useRef<HTMLDivElement>(null)

    const [showInstructionsDialog, setShowInstructionsDialog] = useState(false)
    const [instructionsText, setInstructionsText] = useState("")
    const [requiresMedicalOrder, setRequiresMedicalOrder] = useState(false)
    const [copayAmount, setCopayAmount] = useState<number | null>(null)
    const [isConsultationCoverageExcluded, setIsConsultationCoverageExcluded] = useState(false)
    const [selectedConsultationType, setSelectedConsultationType] = useState("")
    const [selectedCoverage, setSelectedCoverage] = useState("")
    const [isCoverageAccepted, setIsCoverageAccepted] = useState<boolean | null>(null)

    // Helper to check if a consultation type has indications, non-accepted consultation types, or copays
    const hasConsultationTypeInfo = useCallback((typeName: string, coverage?: string, doctor?: Doctor): boolean => {
        if (!typeName || !doctor || !doctor.consultationTypes) return false

        const consultationType = doctor.consultationTypes.find(ct => ct.name === typeName)
        if (!consultationType) return false

        // Check if the consultation type has instructions
        const hasInstructions = !!(consultationType.hasInstructions && consultationType.instructions)

        // Check if the consultation type requires a medical order
        const requiresOrder = !!consultationType.requiresMedicalOrder

        // Check for coverage-related info (copays, exclusions)
        let hasCoverageInfo = false

        if (coverage) {
            if (coverage === "Sin Cobertura") {
                // Check if consultation type accepts private pay
                hasCoverageInfo = consultationType.acceptsPrivatePay === false
            } else {
                // For other coverages, check for copays and exclusions
                const foundCoverage = DEFAULT_COVERAGES.find(cov => coverage.startsWith(cov.name))
                if (foundCoverage) {
                    const coverageId = foundCoverage.id

                    // Extract plan if present
                    let planId: string | null = null
                    if (coverage !== foundCoverage.name) {
                        const planPart = coverage.substring(foundCoverage.name.length).trim()
                        if (planPart && foundCoverage.plans.includes(planPart)) {
                            planId = planPart
                        }
                    }

                    // Check for copays
                    const hasCopay = consultationType.copays?.some(
                        c => c.coverageId === coverageId &&
                            (c.planId === null || c.planId === planId)
                    )

                    // Check for exclusions
                    const isExcluded = consultationType.excludedCoverages?.some(
                        exclusion =>
                            exclusion.coverageId === coverageId &&
                            (exclusion.planId === null || exclusion.planId === planId)
                    )

                    hasCoverageInfo = !!hasCopay || !!isExcluded
                }
            }
        }

        return hasInstructions || requiresOrder || hasCoverageInfo
    }, [])

    // Function to show consultation type instructions and requirements
    const showConsultationInfo = useCallback((typeName: string, coverage: string, doctor: Doctor) => {
        const consultationType = doctor.consultationTypes.find(t => t.name === typeName)
        if (consultationType) {
            // Check if there's any info to show
            const hasInstructions = consultationType.hasInstructions && consultationType.instructions
            const requiresOrder = consultationType.requiresMedicalOrder

            // Get current coverage information
            let coverageId = ""
            let planId: string | null = null
            let isExcluded = false
            let copay = null

            if (coverage) {
                if (coverage === "Sin Cobertura") {
                    // Handle Sin Cobertura
                    const sinCobertura = DEFAULT_COVERAGES.find(c => c.name === "Sin Cobertura")
                    if (sinCobertura) {
                        coverageId = sinCobertura.id

                        // Check if this consultation type accepts private pay
                        if (consultationType.acceptsPrivatePay === false) {
                            isExcluded = true
                        }
                    }
                } else {
                    // Extract base coverage name and plan
                    const foundCoverage = DEFAULT_COVERAGES.find(cov => coverage.startsWith(cov.name))
                    if (foundCoverage) {
                        coverageId = foundCoverage.id

                        // Extract plan if present
                        if (coverage !== foundCoverage.name) {
                            const planPart = coverage.substring(foundCoverage.name.length).trim()
                            if (planPart && foundCoverage.plans.includes(planPart)) {
                                planId = planPart
                            }
                        }

                        // Check if this coverage is excluded for this consultation type
                        isExcluded = consultationType.excludedCoverages?.some(
                            exclusion =>
                                exclusion.coverageId === coverageId &&
                                (exclusion.planId === null || exclusion.planId === planId)
                        ) || false

                        // Check if there's a copay for this coverage
                        const foundCopay = consultationType.copays?.find(
                            c => c.coverageId === coverageId &&
                                (c.planId === null || c.planId === planId)
                        )

                        if (foundCopay) {
                            copay = foundCopay.amount
                        }
                    }
                }
            }

            // Update state with coverage and copay information
            setIsConsultationCoverageExcluded(isExcluded)
            setCopayAmount(copay)
            setSelectedConsultationType(typeName)
            setSelectedCoverage(coverage)

            // Update coverage acceptance state
            setIsCoverageAccepted(!isExcluded)

            // Check if there's any info to show (including copay)
            if (!hasInstructions && !requiresOrder && copay === null && !isExcluded) {
                return // Don't show dialog if there's no info
            }

            let dialogText = ""

            // Add instructions if available
            if (hasInstructions) {
                dialogText += consultationType.instructions
            }

            // Set the dialog content
            setInstructionsText(dialogText)
            setRequiresMedicalOrder(requiresOrder)
            setShowInstructionsDialog(true)
        }
    }, [])

    useEffect(() => {
        if (isOpen) {
            const appointmentDate = new Date(appointment.date + "T00:00:00")
            const currentDoctor = doctors.find(d => d.id === doctorId)
            setSelectedDate(appointmentDate)
            setModifyTime(appointment.startTime)
            setIsDateChanged(false)
            setSelectedTypes(appointment.consultationTypes)
            setSelectedDoctor(currentDoctor || null)
            setIsCoverageAccepted(null) // Reset coverage acceptance state
        }
    }, [isOpen, appointment, doctors, doctorId])

    // Check if anything has changed from the original appointment
    const hasChanges = () => {
        if (!selectedDoctor) return false

        // Check if doctor changed
        if (selectedDoctor.id !== doctorId) return true

        // Check if date changed
        const originalDate = new Date(appointment.date + "T00:00:00")
        if (selectedDate.getTime() !== originalDate.getTime()) return true

        // Check if time changed
        if (modifyTime !== appointment.startTime) return true

        // Check if consultation types changed
        const originalTypes = appointment.consultationTypes
        if (selectedTypes.length !== originalTypes.length) return true

        // Check if any type is different
        for (const type of selectedTypes) {
            if (!originalTypes.includes(type)) return true
        }

        return false
    }

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (consultationRef.current && !consultationRef.current.contains(e.target as Node)) {
                setShowConsultationDropdown(false)
                setConsultationSearch("")
            }
        }
        document.addEventListener("mousedown", handleClickOutside)
        return () => document.removeEventListener("mousedown", handleClickOutside)
    }, [])

    if (!isOpen) return null

    const isDateAvailable = (date: Date) => {
        if (!selectedDoctor) return false

        const dateStr = date.toISOString().split("T")[0]
        const dayOfWeek = date.getDay().toString()

        // Check if there's an explicit date exception
        if (selectedDoctor.dateExceptions && dateStr in selectedDoctor.dateExceptions) {
            // If it's an extraordinary day (enabled with hours), it's available
            const exception = selectedDoctor.dateExceptions[dateStr]
            if (exception.enabled && exception.hours && exception.hours.length > 0) {
                return true
            }
            // Otherwise, use the enabled status
            return exception.enabled
        }

        const workingDay = selectedDoctor.workingDays[dayOfWeek]

        if (!workingDay?.enabled) return false

        // Use shared getWeeksDifference and REFERENCE_DATE
        const frequency = workingDay.weeksFrequency || 1
        const weeksSinceReference = getWeeksDifference(date, REFERENCE_DATE)
        return weeksSinceReference % frequency === 0
    }

    const getAvailableTimeSlots = () => {
        if (!selectedDoctor || !selectedDate) return [];

        const dateStr = selectedDate.toISOString().split("T")[0];
        const dayOfWeek = selectedDate.getDay().toString();

        // Check for extraordinary day agenda first
        const dateExceptions = selectedDoctor.dateExceptions || {};
        const hasExtraordinaryDay = dateStr in dateExceptions &&
            dateExceptions[dateStr].enabled &&
            dateExceptions[dateStr].hours &&
            dateExceptions[dateStr].hours!.length > 0;

        // Get the hours to use - either from extraordinary day or regular working day
        let hoursToUse: Array<{ start: string; end: string }> = [];

        if (hasExtraordinaryDay) {
            // Use extraordinary day hours
            hoursToUse = dateExceptions[dateStr].hours || [];
        } else {
            // Use regular working day hours if available
            const workingDay = selectedDoctor.workingDays[dayOfWeek];
            if (!workingDay?.enabled || !workingDay.hours || workingDay.hours.length === 0) return [];
            hoursToUse = workingDay.hours;
        }

        // If no hours are available, return empty array
        if (hoursToUse.length === 0) return [];

        const slotDuration = selectedDoctor.appointmentSlotDuration || 15;
        const consultationTypes = selectedDoctor.consultationTypes || [];

        const maxDuration = selectedTypes.length > 0
            ? Math.max(
                ...selectedTypes.map((typeName) => {
                    const consultation = consultationTypes.find((t) => t.name === typeName);
                    const durationValue = consultation?.duration || "default"; // Fallback to "default" if undefined
                    const multiplier = durationValue === "default"
                        ? 1
                        : isNaN(parseInt(durationValue))
                            ? 1
                            : parseInt(durationValue);
                    return slotDuration * multiplier;
                })
            )
            : slotDuration;

        const generateTimeSlots = (start: string, end: string, durationMinutes: number) => {
            const slots: string[] = [];
            const startDate = new Date(`2023-01-01T${start}`);
            const endDate = new Date(`2023-01-01T${end}`);
            endDate.setMinutes(endDate.getMinutes() - durationMinutes);

            const currentTime = new Date(startDate);

            while (currentTime <= endDate) {
                const hours = currentTime.getHours().toString().padStart(2, "0");
                const minutes = currentTime.getMinutes().toString().padStart(2, "0");
                slots.push(`${hours}:${minutes}`);
                currentTime.setMinutes(currentTime.getMinutes() + durationMinutes);
            }

            return slots;
        };

        let allSlots: string[] = [];
        hoursToUse.forEach((hourRange) => {
            const {start, end} = hourRange;
            const rangeSlots = generateTimeSlots(start, end, maxDuration);
            allSlots = [...allSlots, ...rangeSlots];
        });

        return allSlots.sort((a, b) => {
            const timeToMinutes = (time: string) => {
                const [hours, minutes] = time.split(":").map(Number);
                return hours * 60 + minutes;
            };
            return timeToMinutes(a) - timeToMinutes(b);
        });
    };

    const dayHasAppointments = (dateStr: string) => {
        return appointments[dateStr]?.some(
            apt => apt === selectedDoctor?.id && apt.status !== "Cancelado"
        ) || false
    }

    const renderCalendarDays = () => {
        const daysInMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth() + 1, 0).getDate()
        const firstDay = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1)
        const startingDay = (firstDay.getDay() + 6) % 7 // Adjust to start week on Monday

        return [
            ...Array.from({length: startingDay}).map((_, i) => (
                <div key={`empty-${i}`} className="p-2"/>
            )),
            ...Array.from({length: daysInMonth}, (_, i) => {
                const date = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), i + 1)
                const dateStr = date.toISOString().split("T")[0]

                const isAvailable = isDateAvailable(date)
                const hasAppointments = dayHasAppointments(dateStr)
                const isSelected =
                    selectedDate.getDate() === date.getDate() &&
                    selectedDate.getMonth() === date.getMonth() &&
                    selectedDate.getFullYear() === date.getFullYear()

                return (
                    <button
                        key={i}
                        onClick={() => {
                            if (isAvailable) {
                                setSelectedDate(date)
                                setModifyTime(null)
                                setIsDateChanged(true)
                            }
                        }}
                        disabled={!isAvailable}
                        className={`
              group w-8 h-8 p-0 rounded-full relative
              ${!isAvailable ? "text-gray-300 cursor-not-allowed" : ""}
              ${isSelected ? "bg-blue-500 text-white" : "hover:bg-blue-50"}
            `}
                    >
                        <div className="w-full h-full flex items-center justify-center">{i + 1}</div>
                        {hasAppointments && (
                            <div
                                className={`
                  absolute left-1/2 -translate-x-1/2
                  w-[0.375rem] h-[0.375rem] rounded-full
                  transition-all duration-200
                  ${isSelected ? "-bottom-[0.625rem]" : "-bottom-[0.125rem] group-hover:-bottom-[0.625rem]"}
                  bg-blue-500
                `}
                            />
                        )}
                    </button>
                )
            })
        ]
    }

    const handleModifyConfirm = () => {
        if (selectedDoctor && selectedDate && modifyTime && selectedTypes.length > 0) {
            const slotDuration = selectedDoctor.appointmentSlotDuration || 15;
            const consultationTypes = selectedDoctor.consultationTypes || [];
            const maxDuration = Math.max(
                ...selectedTypes.map((typeName) => {
                    const consultation = consultationTypes.find((t) => t.name === typeName);
                    const durationValue = consultation?.duration || "default";
                    const multiplier = consultation?.duration === "default"
                        ? 1
                        : isNaN(parseInt(consultation?.duration || ""))
                            ? 1
                            : parseInt(durationValue);
                    return slotDuration * multiplier;
                })
            );

            onModifyAppointment(
                selectedDate,
                modifyTime,
                selectedTypes.join(", "),
                selectedDoctor.id,
                maxDuration
            );
            onClose();
        } else {
            console.error("Missing required fields", {
                doctor: !!selectedDoctor,
                date: !!selectedDate,
                time: !!modifyTime,
                types: selectedTypes.length,
            });
        }
    };

    const getConsultationTypes = () => {
        return selectedDoctor?.consultationTypes || []
    }

    return (
        <>
            <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50">
                <div className="bg-white p-5 rounded-lg shadow-lg w-[650px] flex">
                    <div className="w-1/2 pr-4 border-r border-gray-200">
                        <h3 className="text-lg font-semibold mb-4 text-gray-800">Modificar turno</h3>

                        <div className="space-y-2 mb-4">
                            <Label className="text-sm font-medium text-gray-700">Profesional</Label>
                            <Select
                                value={selectedDoctor?.id || ""}
                                onValueChange={(doctorId) => {
                                    const newDoctor = doctors.find(d => d.id === doctorId) || null
                                    setSelectedDoctor(newDoctor)
                                    setSelectedDate(new Date())
                                    setModifyTime(null)
                                    setSelectedTypes([])
                                    setIsDateChanged(true)
                                }}
                            >
                                <SelectTrigger
                                    className="w-full border-gray-200 focus:ring-blue-500 focus:border-blue-500">
                                    <SelectValue placeholder="Seleccionar profesional">
                                        {selectedDoctor ? selectedDoctor.fullName : "Seleccionar profesional"}
                                    </SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                    {doctors.map((doctor) => (
                                        <SelectItem key={doctor.id} value={doctor.id}>
                                            {doctor.fullName}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="border rounded-lg p-4 bg-white border-black">
                            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium">
                {new Date(selectedDate).toLocaleString('es', {month: 'long', year: 'numeric'})}
              </span>
                                <div className="flex gap-2">
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => {
                                            const prevMonth = new Date(selectedDate)
                                            prevMonth.setMonth(prevMonth.getMonth() - 1)
                                            setSelectedDate(prevMonth)
                                        }}
                                    >
                                        <ChevronLeft className="h-4 w-4"/>
                                    </Button>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => {
                                            const nextMonth = new Date(selectedDate)
                                            nextMonth.setMonth(nextMonth.getMonth() + 1)
                                            setSelectedDate(nextMonth)
                                        }}
                                    >
                                        <ChevronRight className="h-4 w-4"/>
                                    </Button>
                                </div>
                            </div>

                            <div className="grid grid-cols-7 gap-1 text-sm mb-2">
                                {["L", "M", "M", "J", "V", "S", "D"].map((day, i) => (
                                    <div key={i} className="text-center font-medium">
                                        {day}
                                    </div>
                                ))}
                            </div>

                            <div className="grid grid-cols-7 gap-1 gap-y-3 text-sm h-[250px]">
                                {renderCalendarDays()}
                            </div>
                        </div>
                    </div>

                    <div className="w-1/2 pl-4 flex flex-col pt-9 space-y-6">
                        <div className="space-y-2">
                            <Label className="text-sm font-medium text-gray-700">Nuevo horario</Label>
                            <Select
                                value={modifyTime ?? ""}
                                onValueChange={(value) => {
                                    setModifyTime(value)
                                    setIsDateChanged(true)
                                }}
                                disabled={!selectedDoctor}
                            >
                                <SelectTrigger
                                    className="w-full border-gray-200 focus:ring-blue-500 focus:border-blue-500">
                                    <SelectValue placeholder="Seleccionar horario">
                                        {!isDateChanged && modifyTime ? modifyTime : undefined}
                                    </SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                    {getAvailableTimeSlots().map((slot) => {
                                        const dateStr = selectedDate.toISOString().split("T")[0];
                                        const existingAppointments = appointments[dateStr] || [];
                                        const isOccupied = existingAppointments
                                            .filter((apt) => apt.doctorId === selectedDoctor!.id && apt.id !== appointment.id && apt.status !== "Cancelado")
                                            .some((apt) => apt.time === slot);
                                        return (
                                            <SelectItem key={slot} value={slot}>
                                                {slot} {isOccupied && "(Sobreturno)"}
                                            </SelectItem>
                                        );
                                    })}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2" ref={consultationRef}>
                            <Label className="text-sm font-medium text-gray-700">Tipo de atención</Label>
                            <div className="relative">
                                <div className="relative">
                                    <Input
                                        placeholder="Buscar tipo de atención"
                                        value={consultationSearch}
                                        autoComplete="off"
                                        onFocus={() => setShowConsultationDropdown(true)}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            setConsultationSearch(e.target.value);
                                            setShowConsultationDropdown(true);
                                        }}
                                        disabled={!selectedDoctor}
                                    />
                                    <Search
                                        className="absolute right-[0.75rem] top-1/2 transform -translate-y-1/2 opacity-50 w-[1rem] h-[1rem] cursor-pointer"/>
                                </div>

                                {showConsultationDropdown && (
                                    <div
                                        className="absolute z-20 w-full bg-white border border-gray-200 rounded-md mt-[0.25rem] shadow-lg">
                                        <div className="max-h-[15rem] overflow-auto">
                                            {getConsultationTypes()
                                                .filter((type) =>
                                                    type.name.toLowerCase().includes(consultationSearch.toLowerCase())
                                                )
                                                .map((type) => {
                                                    const slotDuration = selectedDoctor?.appointmentSlotDuration || 15;
                                                    const multiplier =
                                                        type.duration === "default"
                                                            ? 1
                                                            : isNaN(parseInt(type.duration))
                                                                ? 1
                                                                : parseInt(type.duration);
                                                    const duration = slotDuration * multiplier;

                                                    // Only show duration if it's not default
                                                    const showDuration = type.duration !== "default";

                                                    return (
                                                        <div
                                                            key={type.name}
                                                            className={cn(
                                                                "p-[0.375rem] text-[0.875rem] hover:bg-gray-100 transition-colors cursor-pointer",
                                                                selectedTypes.includes(type.name)
                                                                    ? "text-white bg-blue-500 hover:bg-blue-400"
                                                                    : "hover:bg-gray-100"
                                                            )}
                                                            onClick={() => {
                                                                // Check if the type is already selected
                                                                const isAlreadySelected = selectedTypes.includes(type.name);

                                                                // If not already selected, check if we need to show info
                                                                if (!isAlreadySelected && selectedDoctor && appointment.healthInsuranceInformation) {
                                                                    // Check if this consultation type has any info to show
                                                                    if (hasConsultationTypeInfo(type.name, appointment.healthInsuranceInformation, selectedDoctor)) {
                                                                        showConsultationInfo(type.name, appointment.healthInsuranceInformation, selectedDoctor);
                                                                    }
                                                                }

                                                                // Update selected types
                                                                setSelectedTypes((prev) =>
                                                                    prev.includes(type.name)
                                                                        ? prev.filter((t) => t !== type.name)
                                                                        : [...prev, type.name]
                                                                );

                                                                // Track that something changed
                                                            }}
                                                        >
                                                            {type.name} {showDuration && `(${duration} min)`}
                                                        </div>
                                                    );
                                                })}
                                        </div>
                                        <div className="border-t p-2 flex justify-between items-center">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => {
                                                    setShowConsultationDropdown(false);
                                                    setConsultationSearch("");
                                                }}
                                                className="text-gray-600 hover:bg-gray-100 h-9"
                                            >
                                                Cancelar
                                            </Button>
                                            <Button
                                                size="sm"
                                                className="bg-blue-500 hover:bg-blue-600 text-white h-9"
                                                onClick={() => {
                                                    setShowConsultationDropdown(false);
                                                    setConsultationSearch("");
                                                }}
                                            >
                                                Confirmar
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className="flex flex-wrap gap-2 mt-2">
                                {selectedTypes.map((type) => (
                                    <span
                                        key={type}
                                        className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs flex items-center"
                                    >
    {type}
                                        {selectedDoctor && appointment.healthInsuranceInformation &&
                                            hasConsultationTypeInfo(type, appointment.healthInsuranceInformation, selectedDoctor) && (
                                                <div
                                                    className="ml-1 cursor-pointer"
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        showConsultationInfo(type, appointment.healthInsuranceInformation, selectedDoctor);
                                                    }}
                                                    aria-label={`Ver información de ${type}`}
                                                    role="button"
                                                    tabIndex={0}
                                                    onKeyDown={(e) => {
                                                        if (e.key === 'Enter' || e.key === ' ') {
                                                            e.stopPropagation();
                                                            showConsultationInfo(type, appointment.healthInsuranceInformation, selectedDoctor);
                                                        }
                                                    }}
                                                >
                                                    <Info
                                                        className="h-3.5 w-3.5 text-blue-500 hover:text-blue-700"/>
                                                </div>
                                            )}
                                        <button
                                            onClick={() => {
                                                setSelectedTypes((prev) => prev.filter((t) => t !== type));
                                            }}
                                            className="ml-1 rounded-full hover:bg-blue-200 px-1"
                                        >
      ×
    </button>
  </span>
                                ))}
                            </div>
                        </div>

                        <div className="flex justify-end gap-3 pt-2 border-t border-gray-100 mt-4">
                            <Button
                                variant="outline"
                                onClick={onClose}
                                className="text-gray-600 border-gray-300 hover:bg-gray-100"
                            >
                                Cancelar
                            </Button>
                            <Button
                                onClick={handleModifyConfirm}
                                className="bg-blue-500 hover:bg-blue-600 text-white"
                                disabled={
                                    !selectedDoctor ||
                                    !modifyTime ||
                                    selectedTypes.length === 0 ||
                                    !hasChanges() ||
                                    isCoverageAccepted === false
                                }
                            >
                                Confirmar
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Instructions Dialog */}
            <AlertDialog open={showInstructionsDialog} onOpenChange={setShowInstructionsDialog}>
                <AlertDialogContent className="max-w-md border border-gray-300 shadow-lg">
                    <AlertDialogHeader className="pb-2 border-b border-gray-200">
                        <AlertDialogTitle className="text-center text-lg font-semibold text-gray-800">Información
                            importante</AlertDialogTitle>
                    </AlertDialogHeader>

                    {/* Content outside of AlertDialogDescription to avoid p > div nesting issue */}
                    <div className="mt-4 space-y-4">
                        {isConsultationCoverageExcluded && (
                            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                                <p className="text-red-700 flex items-center gap-2">
                                    <AlertCircle className="h-5 w-5 text-red-500 flex-shrink-0"/>
                                    <span>
                La atención <span className="font-semibold">{selectedConsultationType}</span> no es cubierta por el profesional con la cobertura <span
                                        className="font-semibold">{selectedCoverage}</span>.
              </span>
                                </p>
                            </div>
                        )}

                        {requiresMedicalOrder && (
                            <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
                                <p className="text-amber-700 flex items-center gap-2">
                                    <AlertTriangle className="h-5 w-5 text-amber-500 flex-shrink-0"/>
                                    <span>Esta atención requiere orden médica.</span>
                                </p>
                            </div>
                        )}

                        {copayAmount !== null && (
                            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <p className="text-blue-700 flex items-center gap-2">
                                    <Info className="h-5 w-5 text-blue-500 flex-shrink-0"/>
                                    <span>
                Copago por <span className="font-semibold">{selectedConsultationType}</span> con plan <span
                                        className="font-semibold">{selectedCoverage}</span>: <span
                                        className="font-semibold">${copayAmount}</span>
              </span>
                                </p>
                            </div>
                        )}

                        {instructionsText && (
                            <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                <div className="text-gray-700">
                                    <p className="font-medium mb-2">Indicaciones para atención <span
                                        className="font-semibold">{selectedConsultationType}</span>:</p>
                                    <p className="whitespace-pre-line">
                                        {instructionsText}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className="flex justify-center mt-6">
                        <AlertDialogAction
                            className="bg-blue-500 hover:bg-blue-600 text-white font-medium px-6 py-2 rounded-md transition-colors">
                            Entendido
                        </AlertDialogAction>
                    </div>
                </AlertDialogContent>
            </AlertDialog>
        </>
    )
}
