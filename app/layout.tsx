import "./globals.css"
import "./font-sizes.css"
import type {Metadata, Viewport} from "next"
import {Inter} from "next/font/google"
import localFont from "next/font/local"
import {AppProvider} from "@/contexts/AppContext"
import {AppointmentProvider} from "@/contexts/AppointmentContext"
import {PatientProvider} from "@/contexts/PatientContext"
import {ScheduleChangesProvider} from "@/contexts/ScheduleChangesContext"
import {MedicalCenterEventsProvider} from "@/contexts/MedicalCenterEventsContext"
import type React from "react"
import {ToastContainer} from "react-toastify"
import "react-toastify/dist/ReactToastify.css"
import {ToastProvider} from '@/components/ui/toast'
import { Toaster } from "@/components/ui/sonner"
import {AuthProvider} from "@/contexts/AuthContext"
import Auth0AppProvider from "@/contexts/Auth0Context"
import { Analytics } from '@vercel/analytics/next'
import { SpeedInsights } from '@vercel/speed-insights/next'

const inter = Inter({subsets: ["latin"]})

const recoleta = localFont({
    src: [
        {
            path: '../public/fonts/Recoleta-Regular.woff2',
            weight: '400',
            style: 'normal',
        },
        {
            path: '../public/fonts/Recoleta-Medium.woff2',
            weight: '500',
            style: 'normal',
        },
        {
            path: '../public/fonts/Recoleta-SemiBold.woff2',
            weight: '600',
            style: 'normal',
        },
        {
            path: '../public/fonts/Recoleta-Bold.woff2',
            weight: '700',
            style: 'normal',
        },
    ],
    variable: '--font-recoleta',
})

export const metadata: Metadata = {
    title: "Turnera - Buscá y Reservá turnos médicos",
    description: "Buscá y Reservá turnos médicos",
}

export const viewport: Viewport = {
    width: "device-width",
    initialScale: 1,
    maximumScale: 5,
    userScalable: false,
}

export default function RootLayout({
                                       children,
                                   }: {
    children: React.ReactNode
}) {
    return (
        <html lang="es" className={`${inter.className} ${recoleta.variable}`}>
        <body>
        <Auth0AppProvider>
            <AuthProvider>
                <MedicalCenterEventsProvider>
                    <AppProvider>
                        <PatientProvider>
                            <AppointmentProvider>
                                <ScheduleChangesProvider>
                                    <ToastProvider>
                                        {children}
                                         <Toaster position="top-center" richColors />
                                        <ToastContainer
                                            position="top-right"
                                            autoClose={3000}
                                            hideProgressBar={false}
                                            newestOnTop={false}
                                            closeOnClick
                                            rtl={false}
                                            pauseOnFocusLoss
                                            draggable
                                            pauseOnHover
                                        />
                                    </ToastProvider>
                                </ScheduleChangesProvider>
                            </AppointmentProvider>
                        </PatientProvider>
                    </AppProvider>
                </MedicalCenterEventsProvider>
            </AuthProvider>
        </Auth0AppProvider>
        <Analytics />
        <SpeedInsights />
        </body>
        </html>
    )
}